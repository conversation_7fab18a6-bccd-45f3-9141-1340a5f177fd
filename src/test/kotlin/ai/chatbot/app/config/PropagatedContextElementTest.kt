package ai.chatbot.app.config

import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.context.propagation.slf4j.MdcPropagationContext
import io.micronaut.core.propagation.PropagatedContext
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.slf4j.MDC
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors

@MicronautTest
class PropagatedContextElementTest : StringSpec({

    "should propagate MDC context to coroutine" {
        val testKey = "test-key"
        val testValue = "test-value"
        
        // Setup MDC context
        MDC.put(testKey, testValue)
        
        val result = CompletableFuture<String>()
        
        // Launch coroutine with PropagatedContextElement
        CoroutineScope(Dispatchers.IO + PropagatedContextElement()).launch {
            result.complete(MDC.get(testKey))
        }
        
        // Verify context was propagated
        result.get() shouldBe testValue
        
        // Cleanup
        MDC.clear()
    }

    "should propagate tenant context to coroutine" {
        val tenantId = "test-tenant-123"
        
        // Setup PropagatedContext with tenant
        val scope = PropagatedContext.getOrEmpty()
            .plus(MdcPropagationContext(mapOf(TENANT_KEY to tenantId)))
            .propagate()
        
        scope.use {
            val result = CompletableFuture<String>()
            
            // Launch coroutine with PropagatedContextElement
            CoroutineScope(Dispatchers.IO + PropagatedContextElement()).launch {
                val propagatedContext = PropagatedContext.get()
                val mdcContext = propagatedContext
                    .findAll(MdcPropagationContext::class.java)
                    .firstOrNull { it.state.containsKey(TENANT_KEY) }
                
                result.complete(mdcContext?.state?.get(TENANT_KEY))
            }
            
            // Verify tenant was propagated
            result.get() shouldBe tenantId
        }
    }

    "should maintain PropagatedContext.exists() in coroutine" {
        // Setup PropagatedContext
        val scope = PropagatedContext.getOrEmpty()
            .plus(MdcPropagationContext(mapOf("some-key" to "some-value")))
            .propagate()
        
        scope.use {
            val result = CompletableFuture<Boolean>()
            
            // Launch coroutine with PropagatedContextElement
            CoroutineScope(Dispatchers.IO + PropagatedContextElement()).launch {
                result.complete(PropagatedContext.exists())
            }
            
            // Verify PropagatedContext exists in coroutine
            result.get() shouldBe true
        }
    }

    "should lose context without PropagatedContextElement" {
        val testKey = "test-key"
        val testValue = "test-value"
        
        // Setup MDC context
        MDC.put(testKey, testValue)
        
        val result = CompletableFuture<String?>()
        
        // Launch coroutine WITHOUT PropagatedContextElement
        CoroutineScope(Dispatchers.IO).launch {
            result.complete(MDC.get(testKey))
        }
        
        // Verify context was lost
        result.get() shouldBe null
        
        // Cleanup
        MDC.clear()
    }

    "should work with custom executor" {
        val testKey = "executor-test"
        val testValue = "executor-value"
        val customExecutor = Executors.newFixedThreadPool(2)
        
        try {
            // Setup MDC context
            MDC.put(testKey, testValue)
            
            val result = CompletableFuture<String>()
            
            // Launch coroutine with custom executor + PropagatedContextElement
            CoroutineScope(customExecutor.asCoroutineDispatcher() + PropagatedContextElement()).launch {
                result.complete(MDC.get(testKey) ?: "null")
            }
            
            // Verify context was propagated
            result.get() shouldBe testValue
            
        } finally {
            customExecutor.shutdown()
            MDC.clear()
        }
    }

    "should restore context after coroutine completes" {
        val originalKey = "original-key"
        val originalValue = "original-value"
        val coroutineKey = "coroutine-key"
        val coroutineValue = "coroutine-value"
        
        // Setup original MDC context
        MDC.put(originalKey, originalValue)
        
        runBlocking {
            // Launch coroutine that modifies MDC
            CoroutineScope(Dispatchers.IO + PropagatedContextElement()).launch {
                MDC.put(coroutineKey, coroutineValue)
                // Context should be isolated within coroutine
            }.join()
        }
        
        // Verify original context is preserved
        MDC.get(originalKey) shouldBe originalValue
        MDC.get(coroutineKey) shouldBe null // Should not leak
        
        // Cleanup
        MDC.clear()
    }
})

// Extension function for easier testing
private fun java.util.concurrent.ExecutorService.asCoroutineDispatcher() = 
    kotlinx.coroutines.asCoroutineDispatcher()
