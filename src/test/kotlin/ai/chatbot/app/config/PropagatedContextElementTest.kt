package ai.chatbot.app.config

import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe
import io.micronaut.context.propagation.slf4j.MdcPropagationContext
import io.micronaut.core.propagation.PropagatedContext
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import java.util.concurrent.CompletableFuture
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.slf4j.MDC

class PropagatedContextElementTest : StringSpec({

    "should propagate MDC context to coroutine" {
        val testKey = "test-key"
        val testValue = "test-value"

        // Setup MDC context
        MDC.put(testKey, testValue)

        val result = CompletableFuture<String>()

        // Launch coroutine with PropagatedContextElement
        CoroutineScope(Dispatchers.IO + PropagatedContextElement()).launch {
            result.complete(MDC.get(testKey))
        }

        // Verify context was propagated
        result.get() shouldBe testValue

        // Cleanup
        MDC.clear()
    }

    "should propagate tenant context to coroutine" {
        val tenantId = "test-tenant-123"

        // Setup MDC with tenant
        MDC.put(TENANT_KEY, tenantId)

        val result = CompletableFuture<String>()

        // Launch coroutine with PropagatedContextElement
        CoroutineScope(Dispatchers.IO + PropagatedContextElement()).launch {
            result.complete(MDC.get(TENANT_KEY))
        }

        // Verify tenant was propagated
        result.get() shouldBe tenantId

        // Cleanup
        MDC.clear()
    }

    "should maintain PropagatedContext.exists() in coroutine" {
        // Setup MDC context
        MDC.put("some-key", "some-value")

        val result = CompletableFuture<Boolean>()

        // Launch coroutine with PropagatedContextElement
        CoroutineScope(Dispatchers.IO + PropagatedContextElement()).launch {
            result.complete(PropagatedContext.exists())
        }

        // Verify PropagatedContext exists in coroutine
        result.get() shouldBe true

        // Cleanup
        MDC.clear()
    }

    "should lose context without PropagatedContextElement" {
        val testKey = "test-key"
        val testValue = "test-value"

        // Setup MDC context
        MDC.put(testKey, testValue)

        val result = CompletableFuture<String?>()

        // Launch coroutine WITHOUT PropagatedContextElement
        CoroutineScope(Dispatchers.IO).launch {
            result.complete(MDC.get(testKey))
        }

        // Verify context was lost
        result.get() shouldBe null

        // Cleanup
        MDC.clear()
    }
})
