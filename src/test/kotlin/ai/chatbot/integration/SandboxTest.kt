package ai.chatbot.integration

import DynamoD<PERSON><PERSON>tils.setupDynamoDB
import ai.chatbot.adapters.api.TransactionPaymentStatus
import ai.chatbot.adapters.dynamodb.ChatHistoryDbRepository
import ai.chatbot.adapters.dynamodb.ChatHistoryDynamoDAO
import ai.chatbot.adapters.dynamodb.ChatHistoryStateDbRepository
import ai.chatbot.adapters.dynamodb.ChatHistoryStateDynamoDAO
import ai.chatbot.adapters.messaging.ChatBotNotificationGatewayHandler
import ai.chatbot.adapters.openai.OpenAIAdapter
import ai.chatbot.app.FRIDAY_ENV
import ai.chatbot.app.HistoryRepository
import ai.chatbot.app.HistoryStateRepository
import ai.chatbot.app.ManualEntryId
import ai.chatbot.app.NotificationService
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.PixLimitStatus
import ai.chatbot.app.PixValidationResult
import ai.chatbot.app.SweepingConsent
import ai.chatbot.app.TransactionService
import ai.chatbot.app.balance
import ai.chatbot.app.bill.PendingBillsService
import ai.chatbot.app.billView
import ai.chatbot.app.billView2
import ai.chatbot.app.billView3
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.contact.Contact
import ai.chatbot.app.conversation.ActionExecutorLocator
import ai.chatbot.app.conversation.ActionsManager
import ai.chatbot.app.conversation.BillComingDueHistoryState
import ai.chatbot.app.conversation.ConversationHistoryService
import ai.chatbot.app.conversation.ConversationProcessor
import ai.chatbot.app.conversation.ConversationService
import ai.chatbot.app.conversation.HistoryStateType
import ai.chatbot.app.conversation.InteractionWindowService
import ai.chatbot.app.conversation.InternalStateControl
import ai.chatbot.app.conversation.MessageType
import ai.chatbot.app.conversation.SingleChatMessageHandler
import ai.chatbot.app.conversation.SubscriptionType
import ai.chatbot.app.conversation.actions.ActionExecutorsHelpers
import ai.chatbot.app.getCustomNotificationServiceMock
import ai.chatbot.app.newActionExecutorLocator
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.FridayNotificationContextTemplatesService
import ai.chatbot.app.notification.FridayOnboardingSinglePixNotificationService
import ai.chatbot.app.pix.PixKey
import ai.chatbot.app.pix.PixKeyType
import ai.chatbot.app.pix.PixQRCode
import ai.chatbot.app.subscription
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.AccountId
import ai.chatbot.app.user.AccountStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserAndWallet
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.user.WalletWithBills
import ai.chatbot.app.utils.getObjectMapper
import ai.integration.chatbot.utils.OpenAITokenCondition
import arrow.core.right
import io.kotest.core.annotation.EnabledIf
import io.kotest.core.spec.style.DescribeSpec
import io.micronaut.context.ApplicationContext
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import jakarta.inject.Named
import java.util.concurrent.ExecutorService

@EnabledIf(OpenAITokenCondition::class)
@MicronautTest(environments = [FRIDAY_ENV, "test"])
class SandboxTest(
    openAIAdapter: OpenAIAdapter,
    applicationContext: ApplicationContext,
    @Named("conversation-executor") conversationExecutor: ExecutorService,
) : DescribeSpec() {
    private val tenantService = mockk<TenantService>()

    private val dynamoDbEnhancedClient = setupDynamoDB()
    private lateinit var historyRepository: HistoryRepository
    private lateinit var historyStateRepository: HistoryStateRepository
    private val paymentAdapter = mockk<PaymentAdapter>()
    private val notificationService: NotificationService = mockk(relaxed = true)
    private val transactionService: TransactionService = mockk(relaxed = true)
    private val interactionWindowService: InteractionWindowService =
        mockk {
            every { checkAndCreate(any()) } returns Unit.right()
        }
    private lateinit var pendingBillsService: PendingBillsService

    private lateinit var conversationHistoryService: ConversationHistoryService
    private lateinit var customNotificationService: CustomNotificationService

    private lateinit var locator: ActionExecutorLocator

    private lateinit var actionExecutorsHelpers: ActionExecutorsHelpers
    private lateinit var actionExecutorLocator: ActionExecutorLocator
    private lateinit var actionsManager: ActionsManager

    private lateinit var baseProcessor: ConversationProcessor

    private lateinit var singleMessageProcessor: SingleChatMessageHandler

    private lateinit var conversationService: ConversationService
    private val pixCode =
        "<EMAIL>.br52040000530398654040.015802BR5910JOAO PEDRO6014RIO DE JANEIRO61082022029762100506CecApp6304A5F9"

    private val isBetaAccount = false // Se verdadeiro seta o modelo novo de LLM
    private val user =
        User(
            accountId = AccountId(value = "ACCOUNT-ID"),
            id = if (isBetaAccount) UserId("*************") else UserId("*************"),
            name = "João da Silva Sauro",
            accountGroups = listOf("ALPHA"),
            status = AccountStatus.ACTIVE,
        )

    private val walletId = WalletId("walletId")

    private val billViews = listOf(billView, billView2, billView3)

    private val billView3Subscription = billView3.copy(subscriptionFee = true, billDescription = "TESTE TESTE APAGADR")

    private val billComingDueState =
        BillComingDueHistoryState(
            walletWithBills =
            WalletWithBills(
                bills = billViews,
                walletId = walletId,
                walletName = "walletName",
            ),
            balance = null,
            internalStateControl = InternalStateControl(shouldSynchronizeBeforeCompletion = true, billComingDueNotifiedAt = null),
            user = user,
            contacts = emptyList(),
            subscription = null, // TODO: o que vai acontecer quando passar a receber a subscription
        )

    private fun createUserHistory() {
        historyRepository.create(
            userId = user.id,
            historyStateType = HistoryStateType.BILLS_COMING_DUE,
            initialUserMessage = null,
        )
    }

    private fun printConversation() {
        val conversationHistory = historyRepository.findLatest(user.id)

        println("-------------------------------------------")

        conversationHistory.messages.forEach { entry ->

            when (entry.type) {
                MessageType.SYSTEM -> {
                    println("SYSTEM: ${entry.message}")
                }

                MessageType.USER -> {
                    println("Usuário:\n${entry.message}")
                }

                MessageType.ASSISTANT -> {
                    println("Fred:")
                    println(entry.message ?: "-")

                    entry.completionMessage?.let { completion ->
                        println("\n(${completion.entendimento})")
                        completion.acoes.forEach {
                            println("Action: ${getObjectMapper().writeValueAsString(it)}")
                        }
                    }
                }

                else -> {
                    println("-")
                }
            }

            println()
        }

        println("-------------------------------------------")
    }

    private lateinit var messageHandler: ChatBotNotificationGatewayHandler

    private val onboardingSinglePixNotificationService = FridayOnboardingSinglePixNotificationService()
    private val notificationContextTemplatesService = FridayNotificationContextTemplatesService()

    val pixKey: PixKey = mockk(relaxed = true)

    init {

        beforeEach {
            clearAllMocks()

            historyRepository = ChatHistoryDbRepository(ChatHistoryDynamoDAO(dynamoDbEnhancedClient, applicationContext), tenantService)
            historyStateRepository = ChatHistoryStateDbRepository(ChatHistoryStateDynamoDAO(dynamoDbEnhancedClient, applicationContext), tenantService)

            val buildNotificationService = BuildNotificationService(notificationContextTemplatesService, tenantService = tenantService)

            pendingBillsService =
                PendingBillsService(
                    historyStateRepository = historyStateRepository,
                    paymentAdapter = paymentAdapter,
                )

            conversationHistoryService =
                ConversationHistoryService(
                    historyRepository = historyRepository,
                    openAIAdapter = openAIAdapter,
                    paymentAdapter = paymentAdapter,
                    notificationService = notificationService,
                    historyStateRepository = historyStateRepository,
                    pendingBillsService = pendingBillsService,
                    promptService = mockk(),
                )

            actionExecutorsHelpers = ActionExecutorsHelpers(
                conversationHistoryService = conversationHistoryService,
                notificationService = notificationService,
                transactionService = transactionService,
                paymentAdapter = paymentAdapter,
                buildNotificationService = buildNotificationService,
                notificationContextTemplatesService = notificationContextTemplatesService,
                onePixPayInstrumentation = mockk(relaxed = true),
                customNotificationService = getCustomNotificationServiceMock(),
            )

            customNotificationService = CustomNotificationService(
                notificationService = notificationService,
                conversationHistoryService = conversationHistoryService,
                templateInfoService = mockk(relaxed = true),
                tenantService = tenantService,
                notificationContextTemplatesService = mockk(relaxed = true),
            )

            locator =
                newActionExecutorLocator(
                    notificationService = notificationService,
                    paymentAdapter = paymentAdapter,
                    conversationHistoryService = conversationHistoryService,
                    transactionService = transactionService,
                    openAIAdapter = openAIAdapter,
                    pendingBillsService = pendingBillsService,
                    buildNotificationService = buildNotificationService,
                    notificationContextTemplatesService = notificationContextTemplatesService,
                    customNotificationService = customNotificationService,
                    actionExecutorsHelpers = actionExecutorsHelpers,
                )

            actionExecutorLocator = spyk(locator)

            actionsManager = spyk(ActionsManager(actionExecutorLocator, conversationHistoryService, notificationService))

            baseProcessor =
                ConversationProcessor(
                    actionsManager = actionsManager,
                    conversationHistoryService = conversationHistoryService,
                    notificationService = notificationService,
                    transactionService = transactionService,
                    onePixPayInstrumentation = mockk(relaxed = true),
                    paymentAdapter = paymentAdapter,
                    buildNotificationService = BuildNotificationService(notificationContextTemplatesService, tenantService = tenantService),
                    notificationContextTemplatesService = notificationContextTemplatesService,
                    eventService = mockk(relaxed = true),
                    tenantService = tenantService,
                    customNotificationService = getCustomNotificationServiceMock(),
                )

            singleMessageProcessor =
                SingleChatMessageHandler(
                    conversationProcessor = baseProcessor,
                    conversationExecutor = conversationExecutor,
                )

            conversationService =
                ConversationService(
                    singleMessageProcessor = singleMessageProcessor,
                    conversationHistoryService = conversationHistoryService,
                    notificationService = notificationService,
                    buildNotificationService = BuildNotificationService(notificationContextTemplatesService, tenantService = tenantService),
                    notificationContextTemplatesService = notificationContextTemplatesService,
                )

            messageHandler =
                ChatBotNotificationGatewayHandler(
                    conversationHistoryService = conversationHistoryService,
                    notificationService = notificationService,
                    queue = "fake_queue",
                    amazonSQS = mockk(),
                    configuration = mockk(),
                    onePixPayInstrumentation = mockk(relaxed = true),
                    interactionWindowService = interactionWindowService,
                    paymentAdapter = mockk { every { getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right() },
                    tenantPropagator = mockk(relaxed = true),
                    tenantService = tenantService,
                    chatbotNotificationBuilder = mockk(relaxed = true),
                )

            customNotificationService = CustomNotificationService(
                notificationService = notificationService,
                conversationHistoryService = conversationHistoryService,
                templateInfoService = mockk(relaxed = true),
                tenantService = tenantService,
                notificationContextTemplatesService = mockk(relaxed = true),
            )

            every { tenantService.getTenantName() } returns "friday"

            coEvery { paymentAdapter.getActiveSweepingConsents(any(), any()) } returns emptyList<SweepingConsent>().right()
            coEvery { paymentAdapter.getContacts(any(), any()) } returns emptyList<Contact>().right()
            coEvery { paymentAdapter.generateOnePixPay(user.id, any(), walletId) } returns PixQRCode(pixCode).right()
            coEvery { paymentAdapter.findPendingBills(any(), any(), any()) } returns UserAndWallet(user, WalletWithBills(billViews, walletId = walletId, "walletName")).right()
            coEvery { paymentAdapter.getSubscription(user.id) } returns subscription.copy(type = SubscriptionType.IN_APP, fee = null).right()

            coEvery {
                paymentAdapter.getBalanceAndForecast(any(), any())
            } returns balance.right()

            historyStateRepository.save(user.id, billComingDueState)
            createUserHistory()
        }

        describe("Teste interativo com o chatbot") {
            it("Deve interagir com o chatbot com as mensagens pré-programadas") {

                coEvery {
                    paymentAdapter.getBalanceAndForecast(any(), any())
                } returns balance.copy(current = 0).right()

                coEvery { paymentAdapter.createManualEntry(any(), any(), any(), any(), any(), any()) } returns ManualEntryId("MANUAL-123").right()

                conversationHistoryService.createUserMessage(user.id, "Me lembrar de pagar boleto da prefeitura amanhã 600")
                baseProcessor.process(user)

                printConversation()
            }

            it("Deve perguntar o pix e não inferir pelo último usado") {

                coEvery {
                    paymentAdapter.getBalanceAndForecast(any(), any())
                } returns balance.copy(current = 10000).right()

                val pixValidationResult = PixValidationResult(
                    keyType = PixKeyType.CNPJ,
                    keyValue = "12345678000196",
                    recipientInstitution = "Itaú",
                    recipientDocument = "154.737.487-08",
                    recipientName = "Mario Gomes",
                    pixLimitStatus = PixLimitStatus.AVAILABLE,
                    e2e = "",
                    amount = 2500,
                )

                coEvery { transactionService.create(any(), any(), any(), any()) } returns Transaction(
                    id = TransactionId("TRANSACTION-ID"),
                    groupId = TransactionGroupId(),
                    userId = user.id,
                    walletId = walletId,
                    status = TransactionStatus.ACTIVE,
                    paymentStatus = TransactionPaymentStatus.UNKNOWN,
                    details = PixTransactionDetails(
                        amount = 50_00,
                        pixKey = PixKey(pixValidationResult.keyValue, pixValidationResult.keyType),
                        recipientName = pixValidationResult.recipientName,
                        recipientDocument = pixValidationResult.recipientDocument,
                        recipientInstitution = pixValidationResult.recipientInstitution,
                        sweepingAmount = null,
                        sweepingParticipantId = null,
                        qrCode = null,
                        hasSubscriptionFee = false,
                    ),
                )

                coEvery { paymentAdapter.pixValidation(match { it.type == PixKeyType.CNPJ }, any(), any(), any()) } returns pixValidationResult.right()

                conversationHistoryService.createUserMessage(user.id, "PIX de R\$ 65 para a chave 123-45-678-000-196.")
                conversationHistoryService.createSystemMessage(user.id, "A mensagem anterior foi transcrita do que o usuário enviou")
                baseProcessor.process(user)

                printConversation()

                conversationHistoryService.createUserMessage(user.id, "PIX para 21993160105")
                conversationHistoryService.createSystemMessage(user.id, "A mensagem anterior foi transcrita do que o usuário enviou")
                baseProcessor.process(user)

                printConversation()
            }

            it("Deve responder sobre contas que vencem hoje") {

                // every { pendingBillsService.updatePendingBills(user.id, any(), any()) } returns UpdatePendingBillsResult().right()

                // conversationHistoryService.createUserMessage(user.id, "quais contas que vencem hoje?")
                // baseProcessor.process(user)
                conversationHistoryService.createUserMessage(user.id, "qual o valor das contas que vencem hoje?")
                baseProcessor.process(user)
                conversationHistoryService.createUserMessage(user.id, "pagar todas as contas que vencem hoje")
                baseProcessor.process(user)

                printConversation()
            }
        }
    }
}