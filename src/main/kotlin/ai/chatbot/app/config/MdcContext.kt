package ai.chatbot.app.config

import kotlin.coroutines.CoroutineContext
import kotlinx.coroutines.ThreadContextElement
import org.slf4j.MDC

class MdcContext(
    private val contextMap: Map<String, String>? = MDC.getCopyOfContextMap(),
) : ThreadContextElement<Map<String, String>?>, CoroutineContext.Element {

    companion object Key : CoroutineContext.Key<MdcContext>

    override val key: CoroutineContext.Key<MdcContext>
        get() = Key

    override fun updateThreadContext(context: CoroutineContext): Map<String, String>? {
        val previous = MDC.getCopyOfContextMap()
        if (contextMap != null) {
            MDC.setContextMap(contextMap)
        } else {
            MDC.clear()
        }
        return previous
    }

    override fun restoreThreadContext(context: CoroutineContext, oldState: Map<String, String>?) {
        if (oldState != null) {
            MDC.setContextMap(oldState)
        } else {
            MDC.clear()
        }
    }
}