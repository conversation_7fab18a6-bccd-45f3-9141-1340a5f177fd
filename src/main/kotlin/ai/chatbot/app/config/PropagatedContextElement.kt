package ai.chatbot.app.config

import io.micronaut.core.propagation.PropagatedContext
import kotlin.coroutines.CoroutineContext
import kotlinx.coroutines.ThreadContextElement

class PropagatedContextElement(
    private val propagatedContext: PropagatedContext = PropagatedContext.getOrEmpty(),
) : ThreadContextElement<PropagatedContext.Scope?>, CoroutineContext.Element {

    companion object Key : CoroutineContext.Key<PropagatedContextElement>

    override val key: CoroutineContext.Key<PropagatedContextElement>
        get() = Key

    override fun updateThreadContext(context: CoroutineContext): PropagatedContext.Scope? {
        return propagatedContext.propagate()
    }

    override fun restoreThreadContext(context: CoroutineContext, oldState: PropagatedContext.Scope?) {
        oldState?.close()
    }
}
