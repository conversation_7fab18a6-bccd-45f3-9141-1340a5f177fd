package ai.chatbot.app.config

import io.micronaut.context.propagation.slf4j.MdcPropagationContext
import io.micronaut.core.propagation.PropagatedContext
import kotlin.coroutines.CoroutineContext
import kotlinx.coroutines.ThreadContextElement
import org.slf4j.MDC

class PropagatedContextElement(
    private val propagatedContext: PropagatedContext = PropagatedContext.getOrEmpty(),
    private val mdcContext: Map<String, String>? = MDC.getCopyOfContextMap(),
) : ThreadContextElement<PropagatedContextElement.State>, CoroutineContext.Element {

    data class State(
        val propagatedScope: PropagatedContext.Scope?,
        val previousMdc: Map<String, String>?,
    )

    companion object Key : CoroutineContext.Key<PropagatedContextElement>

    override val key: CoroutineContext.Key<PropagatedContextElement>
        get() = Key

    override fun updateThreadContext(context: CoroutineContext): State {
        val previousMdc = MDC.getCopyOfContextMap()

        // Restore MDC context
        if (mdcContext != null) {
            MDC.setContextMap(mdcContext)
        } else {
            MDC.clear()
        }

        // Create new PropagatedContext with MDC
        val newPropagatedContext = if (mdcContext != null) {
            propagatedContext.plus(MdcPropagationContext(mdcContext))
        } else {
            propagatedContext
        }

        val propagatedScope = newPropagatedContext.propagate()

        return State(propagatedScope, previousMdc)
    }

    override fun restoreThreadContext(context: CoroutineContext, oldState: State) {
        oldState.propagatedScope?.close()

        if (oldState.previousMdc != null) {
            MDC.setContextMap(oldState.previousMdc)
        } else {
            MDC.clear()
        }
    }
}
