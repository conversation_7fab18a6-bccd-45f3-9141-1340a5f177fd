package ai.chatbot.app.conversation

import ai.chatbot.adapters.waCommCentre.MediaType
import ai.chatbot.adapters.waCommCentre.ProcessedMediaResult
import ai.chatbot.app.NotificationService
import ai.chatbot.app.OnePixPayInstrumentation
import ai.chatbot.app.PaymentAdapter
import ai.chatbot.app.PrintableSealedClass
import ai.chatbot.app.SweepingParticipantId
import ai.chatbot.app.TransactionService
import ai.chatbot.app.bill.BillView
import ai.chatbot.app.bill.toBarCode
import ai.chatbot.app.config.PropagatedContextElement
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.conversation.actions.PromoteSweepingAccountExecutor
import ai.chatbot.app.conversation.actions.toActionMessage
import ai.chatbot.app.event.EventService
import ai.chatbot.app.event.UserEvent
import ai.chatbot.app.media.BoletoInfo
import ai.chatbot.app.notification.BlipPayload
import ai.chatbot.app.notification.BuildNotificationService
import ai.chatbot.app.notification.ChatbotRawTemplatedNotification
import ai.chatbot.app.notification.CustomNotificationService
import ai.chatbot.app.notification.KnownNotificationTypes
import ai.chatbot.app.notification.KnownTemplateConfigurationKeys
import ai.chatbot.app.notification.NotificationContextTemplatesService
import ai.chatbot.app.notification.NotificationFormatter
import ai.chatbot.app.notification.QuickReplyButton
import ai.chatbot.app.prompt.DEFAULT_ERROR_MESSAGE
import ai.chatbot.app.transaction.AddBoletoTransactionDetails
import ai.chatbot.app.transaction.MarkAsPaidOrIgnoreBillsTransactionDetails
import ai.chatbot.app.transaction.PixTransactionDetails
import ai.chatbot.app.transaction.ScheduleBillsTransactionDetails
import ai.chatbot.app.transaction.SendPixCodeTransactionDetails
import ai.chatbot.app.transaction.SweepingTransactionDetails
import ai.chatbot.app.transaction.Transaction
import ai.chatbot.app.transaction.TransactionErrorReason
import ai.chatbot.app.transaction.TransactionGroupId
import ai.chatbot.app.transaction.TransactionId
import ai.chatbot.app.transaction.TransactionResult
import ai.chatbot.app.transaction.TransactionStatus
import ai.chatbot.app.user.User
import ai.chatbot.app.user.UserId
import ai.chatbot.app.user.WalletId
import ai.chatbot.app.utils.BrazilZonedDateTimeSupplier.getLocalDate
import ai.chatbot.app.utils.andAppend
import ai.chatbot.app.utils.containsOnlyEmojis
import ai.chatbot.app.utils.dateFormat
import ai.chatbot.app.utils.getObjectMapper
import ai.chatbot.app.utils.notifyTransactionCanceled
import ai.chatbot.app.utils.notifyTransactionProcessing
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.TaskScheduler
import io.micronaut.scheduling.annotation.Scheduled
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDate
import java.time.format.DateTimeParseException
import java.util.concurrent.ExecutorService
import java.util.concurrent.ScheduledFuture
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val SUBSCRIPTION_FALLBACK_VALUE = 990L

data class MessageContent(
    private val userSentText: String,
    private val processedMediaResult: ProcessedMediaResult? = null,
) {
    val transcription = processedMediaResult?.transcription
    val textExtraction = processedMediaResult?.textExtraction

    val isTextExtraction = !textExtraction.isNullOrBlank()
    val isTranscript = !transcription.isNullOrBlank()
    val isMediaPixQrCode = processedMediaResult?.qrCodeValues?.isNotEmpty() == true
    val isMediaBarCode = processedMediaResult?.boletoInfo?.isNotEmpty() == true

    val noQrCodeFound = processedMediaResult?.qrCodeValues?.isEmpty() ?: false
    val noBarCodeFound = processedMediaResult?.boletoInfo?.isEmpty() ?: false

    val mediaType = processedMediaResult?.mediaType
    val barCodes = mediaBarCodes()

    val content = transcription ?: run {
        val mediaContent = mediaQrCodes() ?: singleBarCode() ?: textExtraction
        "${mediaContent ?: ""}\n\n$userSentText".trim()
    }

    private fun mediaQrCodes(): String? {
        if (processedMediaResult?.qrCodeValues != null && processedMediaResult.qrCodeValues.isNotEmpty()) {
            // TODO hj pegamos só um dos QR codes (já filtrando por PIX), no futuro podemos deixar o usuário escolher qual usar
            return processedMediaResult.qrCodeValues.first()
        }
        return null
    }

    private fun singleBarCode(): String? {
        if (processedMediaResult?.boletoInfo != null && processedMediaResult.boletoInfo.size == 1) {
            return processedMediaResult.boletoInfo.first().let {
                "${it.codigo}\n\nVencimento: ${it.vencimento}"
            }
        }
        return null
    }

    private fun mediaBarCodes(): List<BoletoInfo> {
        if (processedMediaResult?.boletoInfo != null && processedMediaResult.boletoInfo.isNotEmpty()) {
            return processedMediaResult.boletoInfo
        }
        return emptyList()
    }
}

@Singleton
open class ConversationService(
    private val singleMessageProcessor: SingleChatMessageHandler,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationService: NotificationService,
    private val buildNotificationService: BuildNotificationService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
) {
    private val ignorableMessages = listOf("SENT", "RESET")

    open suspend fun asyncProcessChat(
        userIdentifier: UserId,
        messageContent: MessageContent,
        payloadDateValidationResult: UserMessagePayloadValidationResult,
        interceptAction: InterceptAction? = null,
    ) {
        val logName = "ConversationService#asyncProcessChat"
        val markers = append("userId", userIdentifier.value)
            .andAppend("content", messageContent.content)
            .andAppend("isTranscript", messageContent.isTranscript)
            .andAppend("isMediaPixQrCode", messageContent.isMediaPixQrCode)
            .andAppend("payload", interceptAction)
            .andAppend("payloadDateValidationResult", payloadDateValidationResult)

        val upperContent = messageContent.content.uppercase()

        try {
            if (upperContent in ignorableMessages) {
                if (upperContent == "RESET") {
                    runCatching {
                        HistoryStateType.values().forEach {
                            conversationHistoryService.clearHistory(userIdentifier, it, getLocalDate())
                        }
                    }
                }

                logger.info(markers, logName)
                return
            }

            if (messageContent.content.containsOnlyEmojis()) {
                try {
                    conversationHistoryService.createUserReaction(userIdentifier, messageContent.content)
                    logger.info(markers, "$logName/reaction")
                    return
                } catch (e: UserConversationHistoryNotFound) {
                    logger.warn(markers, "$logName/reactionWithoutHistory")
                    // Se o histórico começar com um emoji não sabemos ao que o usuário está reagindo
                    // Nesse caso, seguimos pra salvar como uma mensagem normal
                }
            }

            val shouldTruncateMessage = messageContent.content.length > MAX_MESSAGE_SIZE

            val validatedContent = if (shouldTruncateMessage) {
                messageContent.content.substring(0, MAX_MESSAGE_SIZE)
            } else {
                messageContent.content
            }

            val user = conversationHistoryService.createUserMessage(userIdentifier, validatedContent)

            if (shouldTruncateMessage) {
                val text = "Sua mensagem foi muito longa e pode não ter sido processada por completo."
                notificationService.notify(user.id, user.accountId, text)
                conversationHistoryService.createAssistantMessage(user.id, text)
                logger.warn(markers.andAppend("messageSize", messageContent.content.length), "$logName/messageTooLong")
            }

            if (messageContent.isTranscript) {
                notificationService.notify(
                    user.id,
                    user.accountId,
                    "Transcrição da sua mensagem: ${messageContent.content}",
                )
                conversationHistoryService.createSystemMessage(user.id, "A mensagem anterior foi transcrita do que o usuário enviou")
            }

            if (messageContent.isMediaPixQrCode) {
                conversationHistoryService.createSystemMessage(user.id, "A mensagem anterior foi o QR extraido do arquivo enviado pelo usuário")
            }

            if (messageContent.isMediaBarCode) {
                conversationHistoryService.createSystemMessage(user.id, "A mensagem anterior foi o código de barras e vencimento do boleto extraídos do arquivo enviado pelo usuário")
            }

            if (messageContent.isTextExtraction) {
                conversationHistoryService.createSystemMessage(user.id, "A mensagem anterior foi o texto extraído de uma imagem enviada pelo usuário que pode conter uma chave PIX")
            }

            if (messageContent.noQrCodeFound && messageContent.noBarCodeFound) {
                if (!messageContent.isTextExtraction && !messageContent.isTranscript) {
                    val notificationText = createNoMediaFoundMessage(messageContent)
                    notificationService.notify(user.id, user.accountId, notificationText)
                    conversationHistoryService.createAssistantMessage(user.id, notificationText)
                    return
                }
            }

            when (payloadDateValidationResult) {
                UserMessagePayloadValidationResult.VALID -> {
                    singleMessageProcessor.handle(user, interceptAction)
                }

                UserMessagePayloadValidationResult.OUTDATED -> {
                    val notification = buildNotificationService.buildOutdatedAction(accountId = user.accountId, mobilePhone = user.id.value)
                    notificationService.notify(notification)
                    val message = notificationContextTemplatesService.getOutdatedAction()
                    conversationHistoryService.createAssistantMessage(user.id, message)
                    logger.info(markers, logName)

                    return
                }

                UserMessagePayloadValidationResult.INVALID -> {
                    logger.error(markers.andAppend("invalidMessage", "true"), "$logName#invalidPayload")
                    singleMessageProcessor.handle(user, interceptAction)
                }
            }
            logger.info(markers, logName)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            notificationService.notify(userIdentifier, null, DEFAULT_ERROR_MESSAGE)
            conversationHistoryService.createAssistantMessage(userIdentifier, CompletionMessage.ofMessage(DEFAULT_ERROR_MESSAGE))
            return
        }
    }

    private fun createNoMediaFoundMessage(messageContent: MessageContent): String {
        val (noArquivo, outro) = when (messageContent.mediaType) {
            MediaType.IMAGE -> Pair("na imagem que você enviou", "outra")
            MediaType.DOCUMENT -> Pair("no documento que você enviou", "outro")
            else -> Pair("no arquivo que você enviou", "outro")
        }
        return "Não consegui identificar um PIX ou código de barras $noArquivo. Consegue enviar $outro?"
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ConversationService::class.java)

        const val MAX_MESSAGE_SIZE = 1000
    }
}

interface ChatMessageHandler {
    fun handle(
        user: User,
        interceptAction: InterceptAction? = null,
    )
}

@Singleton
open class SingleChatMessageHandler(
    private val conversationProcessor: ConversationProcessor,
    @Named("conversation-executor") conversationExecutor: ExecutorService,
) : ChatMessageHandler {

    private val conversationDispatcher = conversationExecutor.asCoroutineDispatcher()

    override fun handle(
        user: User,
        interceptAction: InterceptAction?,
    ) {
        CoroutineScope(conversationDispatcher + PropagatedContextElement()).launch {
            conversationProcessor.process(user, interceptAction = interceptAction)
        }
    }
}

@Singleton
@Deprecated("Era utilizado apenas quando era necessário esperar um tempo entre mensagens do usuário")
open class MultipleChatMessageHandler(
    private val conversationProcessor: ConversationProcessor,
    private val configuration: MultipleChatMessageHandlerConfiguration,
    @Named(TaskExecutors.SCHEDULED) private val taskExecutor: TaskScheduler,
) : ChatMessageHandler {
    private val logger = LoggerFactory.getLogger(MultipleChatMessageHandler::class.java)

    private val executors: MutableMap<UserId, ScheduledFuture<*>> = mutableMapOf()

    override fun handle(
        user: User,
        interceptAction: InterceptAction?,
    ) {
        println("executor: ${taskExecutor.javaClass.name}")
        val logName = "MultipleChatMessageHandler#handle"
        val markers = append("userId", user.id.value)

        executors[user.id]?.let {
            if (!it.isDone) {
                markers.andAppend("cancelledPreviousExecutor", "true")
                it.cancel(false)
            }
        }

        val runnable =
            Runnable {
                conversationProcessor.process(user, interceptAction = interceptAction)
            }
        executors[user.id] = taskExecutor.schedule(configuration.nextUserMessageWaitTime, runnable)

        logger.info(markers, logName)
    }

    @Scheduled(fixedDelay = "1m")
    open fun cleanupExecutorsCache() {
        executors.forEach { (key, value) ->
            if (value.isDone) {
                executors.remove(key)
            }
        }
    }
}

@ConfigurationProperties("multiple-chat-message-handler")
interface MultipleChatMessageHandlerConfiguration {
    val nextUserMessageWaitTime: Duration
}

@Singleton
class ConversationProcessor(
    private val actionsManager: ActionsManager,
    private val conversationHistoryService: ConversationHistoryService,
    private val notificationService: NotificationService,
    private val onePixPayInstrumentation: OnePixPayInstrumentation,
    private val transactionService: TransactionService,
    private val paymentAdapter: PaymentAdapter,
    private val buildNotificationService: BuildNotificationService,
    private val notificationContextTemplatesService: NotificationContextTemplatesService,
    private val eventService: EventService,
    private val customNotificationService: CustomNotificationService,
    private val tenantService: TenantService,
) {
    private val logger = LoggerFactory.getLogger(ConversationProcessor::class.java)

    fun process(
        user: User,
        completionEnabled: Boolean = true,
        systemMessageToCompletion: List<ActionResult>? = null,
        interceptAction: InterceptAction? = null,
    ) {
        val logName = "ConversationProcessor#process"

        val markers =
            append("userId", user.id.value)
                .andAppend("accountId", user.accountId.value)
                .andAppend("groups", user.accountGroups)
                .andAppend("payload", interceptAction)
                .andAppend("systemMessageToCompletion", systemMessageToCompletion)

        try {
            val currentUserMessage = getLatestMessageFromHistory(user.id)

            if (systemMessageToCompletion == null && interceptAction != null) {
                val interceptResult = tryInterceptMessage(user, markers, interceptAction, currentUserMessage.message ?: "")

                if (interceptResult == InterceptionResult.INTERCEPTED) {
                    return
                }
            }

            val completions = conversationHistoryService.createCompletionMessage(user, systemMessageToCompletion)

            completions.forEach { completion ->
                val latestUserMessage = getLatestMessageFromHistory(user.id)

                val hasNewMessageFromUser = currentUserMessage != latestUserMessage

                if (hasNewMessageFromUser) {
                    logger.info(markers.andAppend("hasNewMessageFromUser", "true"), logName)
                    return
                }

                markers.andAppend("completion", completion)

                val actions = completion.acoes
                markers.andAppend("actions", actions)

                val validatedActions = actionsManager.validate(actions, user)

                if (validatedActions.isEmpty()) {
                    return
                }

                conversationHistoryService.createAssistantMessage(user.id, completion)

                val nextSystemMessageToCompletion = actionsManager.execute(validatedActions, user)

                val needCompletion = nextSystemMessageToCompletion.filterIsInstance<ActionResult.WithCompletion>().any { it.needCompletion }

                markers.andAppend("needCompletion", needCompletion).andAppend("nextSystemMessageToCompletion", nextSystemMessageToCompletion)
                logger.info(markers, logName)
                if (needCompletion) {
                    markers.andAppend("completionEnabled", completionEnabled)
                    if (completionEnabled) {
                        process(user, false, nextSystemMessageToCompletion)
                    } else {
                        val message = "desistindo da mensagem mesmo com needCompletion true"
                        logger.error(markers.andAppend("ErrorMessage", message), logName)
                        throw IllegalStateException(message)
                    }
                }
            }
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            notificationService.notify(user.id, user.accountId, DEFAULT_ERROR_MESSAGE)
            conversationHistoryService.createAssistantMessage(user.id, CompletionMessage.ofMessage(DEFAULT_ERROR_MESSAGE))
        }
    }

    private fun getLatestMessageFromHistory(userId: UserId): ChatMessageWrapper =
        conversationHistoryService.historyRepository
            .findLatest(userId)
            .messages
            .last { it.type == MessageType.USER }

    private fun tryInterceptMessage(
        user: User,
        markers: LogstashMarker,
        action: InterceptAction,
        message: String,
    ): InterceptionResult {
        val logName = "ConversationProcessor#interceptMessage"

        val isMessageValid = action.type.validateInterception(message)

        if (!isMessageValid) {
            notificationService.notify(
                userId = user.id,
                accountId = user.accountId,
                "Estamos com problemas no momento. Nossa equipe já está atuando para resolver. Tente novamente mais tarde.",
            )
            return InterceptionResult.INTERCEPTED
        }

        val result =
            when (action.type) {
                InterceptMessagePayloadType.MARK_AS_PAID -> interceptMarkAsPaidMessage(user, action)
                InterceptMessagePayloadType.MARK_REMINDER_AS_DONE -> interceptMarkReminderAsDone(user, action.payload)
                InterceptMessagePayloadType.SEND_PIX_CODE -> interceptSendPixCodeMessage(user, action)
                InterceptMessagePayloadType.SEND_SUBSCRIPTION_PIX_CODE -> interceptSendSubscriptionPixCodeMessage(user)
                InterceptMessagePayloadType.TRANSACTION_CONFIRM,
                InterceptMessagePayloadType.TRANSACTION_RETRY,
                InterceptMessagePayloadType.TRANSACTION_CANCEL,
                    -> interceptTransactionMessage(action, user)

                InterceptMessagePayloadType.LIST_BILLS -> interceptListBillsMessage(user, action)
                InterceptMessagePayloadType.ADD_NEW_CONNECTION -> interceptAddNewConnectionMessage(user)
                InterceptMessagePayloadType.ONBOARDING_SINGLE_PIX -> interceptOnboardingSinglePixReplyMessage(user, action.payload)
                InterceptMessagePayloadType.SCHEDULE_BILLS -> interceptSendScheduleBillsMessage(user, action)
                InterceptMessagePayloadType.SCHEDULE_CONFIRM -> interceptSendScheduleBillsMessage(user, action)
                InterceptMessagePayloadType.PROMOTE_SWEEPING_ACCOUNT -> interceptPromoteSweepingAccountMessage(user, action.payload)
                InterceptMessagePayloadType.PAY_SOME -> interceptPaySome(user)
                InterceptMessagePayloadType.RETRY_SWEEPING -> interceptRetrySweepingTransfer(user, action)
                InterceptMessagePayloadType.PAY_BOLETO -> interceptPayBoleto(user, action)
                InterceptMessagePayloadType.ADD_BOLETO -> interceptAddBoleto(user, action)
                InterceptMessagePayloadType.SCHEDULE_BOLETO -> interceptPayBoleto(user, action, true)
                InterceptMessagePayloadType.VALIDATE_BOLETO -> interceptValidateBoleto(user, action)
                InterceptMessagePayloadType.REMOVE_MANUAL_ENTRY -> interceptRemoveManualEntry(user, action.payload)
                InterceptMessagePayloadType.SELECT_SWEEPING_ACCOUNT -> interceptSelectSweepingAccount(user, action)
            }

        logger.info(markers.andAppend("payload", action).andAppend("interceptionStatus", result), logName)
        return result
    }

    private fun InterceptMessagePayloadType.validateInterception(message: String): Boolean {
        if (this.allowEmptyText) {
            return true
        }
        val expectedTexts = this.expectedTexts.map { it.lowercase() }
        val sanitizedMessage = message.lowercase()

        if (sanitizedMessage in expectedTexts) {
            return true
        }

        if (expectedPrefixes.any { sanitizedMessage.startsWith(it, ignoreCase = true) }) {
            return true
        }

        logger.error(
            append("userMessage", message)
                .andAppend("expectedTexts", this.expectedTexts)
                .andAppend("expectedPrefixes", this.expectedPrefixes)
                .andAppend("ACTION", "VERIFY")
                .andAppend("actionName", this.name),
            "validateInterceptionMessage",
        )
        return false
    }

    private fun interceptMessage(
        user: User,
        actionFunc: () -> Action,
    ): InterceptionResult {
        val action = actionFunc()

        conversationHistoryService.createAssistantMessage(
            user.id,
            CompletionMessage(
                verificacaoDeIntegridade = "",
                entendimento = "",
                acoes = listOf(action),
            ),
        )

        val result =
            actionsManager.execute(
                listOf(action),
                user,
            )

        val withCompletionInstances = result.filterIsInstance<ActionResult.WithCompletion>()
        val needCompletion = withCompletionInstances.any { it.needCompletion }
        if (needCompletion) {
            withCompletionInstances.forEach {
                conversationHistoryService.createSystemMessage(user.id, it.systemMessageToCompletion)
            }
            return InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
        }

        return InterceptionResult.INTERCEPTED
    }

    private fun interceptListBillsMessage(user: User, action: InterceptAction): InterceptionResult {
        val logName = "ConversationProcessor#interceptListBillsMessage"
        val markers = append("action", action)
        val date =
            if (action.payload.isNullOrEmpty()) {
                getLocalDate()
            } else {
                try {
                    LocalDate.parse(action.payload, dateFormat)
                } catch (ex: DateTimeParseException) {
                    getLocalDate()
                }
            }

        val state =
            conversationHistoryService.refreshPendingBills(userId = user.id, date, getLocalDate()).getOrElse {
                when (it) {
                    is RefreshUserStateError.PendingBillsOutdated -> {
                        markers.andAppend("BillsOutdated", it)
                        logger.info(markers, logName)
                        onePixPayInstrumentation.billsChangedAfterInteraction(user, InterceptMessagePayloadType.LIST_BILLS)
                        sendBillsOutdatedNotification(user)
                        return InterceptionResult.INTERCEPTED
                    }
                }
            }

        val billsMessage = NotificationFormatter.buildBillNotificationMessage(state.walletWithBills.bills)

        val notifications = buildNotificationService.buildBillsComingDueUserRequestedNotification(user, billsMessage)

        markers.andAppend("notifications", notifications.map { it.message })
        notifications.map { notification ->
            notificationService.notify(
                userId = user.id,
                accountId = user.accountId,
                message = notification.message,
                quickReplyButtons = notification.quickReplyButtons,
            )
            conversationHistoryService.createAssistantMessage(user.id, notification.message)
        }

        logger.info(markers, logName)

        return InterceptionResult.INTERCEPTED
    }

    private fun interceptMarkAsPaidMessage(
        user: User,
        action: InterceptAction,
    ): InterceptionResult {
        val logName = "ConversationProcessor#interceptMarkAsPaidMessage"
        val markers = append("action", action)

        onePixPayInstrumentation.userQuickRepliedMarkAllAsPaid(user)

        val date =
            if (action.payload.isNullOrEmpty()) {
                getLocalDate()
            } else {
                try {
                    LocalDate.parse(action.payload, dateFormat)
                } catch (ex: DateTimeParseException) {
                    getLocalDate()
                }
            }

        val state =
            conversationHistoryService.refreshPendingBills(userId = user.id, startDate = date, endDate = getLocalDate()).getOrElse {
                when (it) {
                    is RefreshUserStateError.PendingBillsOutdated -> {
                        onePixPayInstrumentation.billsChangedAfterInteraction(user, InterceptMessagePayloadType.MARK_AS_PAID)
                        sendBillsOutdatedNotification(user)
                        return InterceptionResult.INTERCEPTED
                    }
                }
            }

        if (date != getLocalDate()) {
            onePixPayInstrumentation.billsChangedAfterInteraction(user, InterceptMessagePayloadType.MARK_AS_PAID)
            sendBillsOutdatedNotification(user)
            return InterceptionResult.INTERCEPTED
        }

        return if (action.transactionId != null && action.transactionId.value.isNotEmpty()) {
            val transaction = transactionService.find(action.transactionId)

            if (transaction == null) {
                val notification = "Esta transação não foi encontrada"
                notificationService.notify(
                    userId = state.user.id,
                    accountId = state.user.accountId,
                    notification,
                )
                logger.error(markers, logName)
                return InterceptionResult.INTERCEPTED
            }

            val details = transaction.details as MarkAsPaidOrIgnoreBillsTransactionDetails

            val billsToMarkAsPaid = state.walletWithBills.bills.filter { bill -> details.billsToMarkAsPaid.find { it == bill.billId } != null }
            val billsToIgnore = state.walletWithBills.bills.filter { bill -> details.billsToIgnore.find { it == bill.billId } != null }

            markers.andAppend("billsToMarkAsPaid", billsToMarkAsPaid.map { it.billId.value })
                .andAppend("billsToIgnore", billsToIgnore.map { it.billId.value })

            conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Usuário clicou no botão 'Sim' para confirmar")
            transactionService.confirm(action.transactionId, user.id)
            logger.info(markers, logName)
            interceptMessage(user) {
                Action.MarkAsPaidOrIgnore(
                    MarkBillsAsPaidOrIgnoreBillsAction(billsToMarkAsPaid = billsToMarkAsPaid.map { it.externalBillId }, billsToIgnoreOrRemove = billsToIgnore.map { it.externalBillId }, userConfirmed = true),
                )
            }
        } else {
            markers.andAppend("bills", state.walletWithBills.bills.map { it.billId.value })

            conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Usuário clicou no botão 'Já paguei'")
            logger.info(markers, logName)
            interceptMessage(user) {
                Action.MarkAsPaidOrIgnore(
                    MarkBillsAsPaidOrIgnoreBillsAction(billsToMarkAsPaid = state.walletWithBills.bills.map { it.externalBillId }, billsToIgnoreOrRemove = emptyList(), userConfirmed = true),
                )
            }
        }
    }

    private fun sendBillsOutdatedNotification(user: User) {
        val state = getBillComingDueState(user = user)

        if (state.walletWithBills.bills.isEmpty()) {
            val notification = "Você não tem nenhuma conta pendente para hoje"
            notificationService.notify(
                userId = state.user.id,
                accountId = state.user.accountId,
                notification,
            )
            conversationHistoryService.createAssistantMessage(userId = user.id, notification)
        } else {
            val formattedBillsMessage = NotificationFormatter.buildBillNotificationMessage(state.walletWithBills.bills)

            val notification = buildNotificationService.buildOutdatedPendingBillsNotification(
                user = state.user,
                formattedBillsMessage = formattedBillsMessage,
            )

            notificationService.notify(
                userId = user.id,
                accountId = user.accountId,
                message = notification.message,
                quickReplyButtons = notification.quickReplyButtons,
            )

            conversationHistoryService.createAssistantMessage(
                userId = user.id,
                message = notification.message,
            )
        }
    }

    private fun interceptMarkReminderAsDone(
        user: User,
        payload: String?,
    ): InterceptionResult {
        val action = MarkReminderAsDoneAction(reminderId = payload ?: "")

        return interceptMessage(user) {
            Action.MarkReminderAsDone(action)
        }
    }

    private fun interceptRemoveManualEntry(
        user: User,
        payload: String?,
    ): InterceptionResult {
        val action = Action.RemoveManualEntry(manualEntryId = payload ?: "")

        return interceptMessage(user) { action }
    }

    private fun interceptSelectSweepingAccount(
        user: User,
        action: InterceptAction,
    ): InterceptionResult {
        if (action.transactionId == null) {
            notificationService.notify(
                userId = user.id,
                accountId = user.accountId,
                "Esta transação não foi encontrada",
            )
            return InterceptionResult.INTERCEPTED
        }

        return interceptMessage(user) { Action.SelectSweepingAccount(transactionGroupId = TransactionGroupId(action.transactionId.value)) }
    }

    private fun interceptAddNewConnectionMessage(
        user: User,
    ): InterceptionResult {
        val notification = buildNotificationService.buildAddNewConnectionNotification(user.accountId, user.id.value)

        return interceptMessage(user) {
            Action.SendNotification(notification)
        }
    }

    private fun interceptOnboardingSinglePixReplyMessage(
        user: User,
        payload: String?,
    ): InterceptionResult {
        val action = OnboardingSinglePixAction(payload!!)

        return interceptMessage(user) {
            Action.OnboardingSinglePix(action)
        }
    }

    private fun interceptPromoteSweepingAccountMessage(
        user: User,
        payload: String?,
    ): InterceptionResult {
        val action = PromoteSweepingAccountAction(payload!!)

        try {
            val actionTO = PromoteSweepingAccountExecutor.ActionTO.parse(payload)

            eventService.send(
                accountId = user.accountId,
                event = when (actionTO.action) {
                    PromoteSweepingAccountExecutor.PromoteSweepingAccountActionEnum.KNOW_MORE -> UserEvent.PROMOTE_SWEEPING_CLICK_KNOW_MORE
                    PromoteSweepingAccountExecutor.PromoteSweepingAccountActionEnum.OPT_OUT -> UserEvent.PROMOTE_SWEEPING_CLICK_OPT_OUT
                },
            )
        } catch (e: Exception) {
            logger.warn(
                append("accountId", user.accountId.value)
                    .andAppend("userId", user.id.value)
                    .andAppend("payload", payload),
                "ConversationService#interceptPromoteSweepingAccountMessage/invalidPayload",
            )
        }

        return interceptMessage(user) {
            Action.PromoteSweepingAccount(action)
        }
    }

    private fun interceptTransactionMessage(
        action: InterceptAction,
        user: User,
    ): InterceptionResult {
        val clickMessage = when (action.type) {
            InterceptMessagePayloadType.TRANSACTION_CONFIRM -> "Usuário confirmou a transação"
            InterceptMessagePayloadType.TRANSACTION_CANCEL -> "Usuário quer cancelar a transação"
            InterceptMessagePayloadType.TRANSACTION_RETRY -> "Usuário quer retentar a transação"
            else -> {
                conversationHistoryService.historyRepository.saveSystemMessage(user.id, "actionType inválido: ${action.type}")
                return InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
            }
        }

        conversationHistoryService.historyRepository.saveSystemMessage(user.id, clickMessage)
        if (action.transactionId == null) {
            conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Id da transação é obrigatório")
            return InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
        }

        val transactionResult = when (action.type) {
            InterceptMessagePayloadType.TRANSACTION_CONFIRM -> {
                notifyTransactionProcessing(user, notificationService, conversationHistoryService)
                transactionService.confirm(action.transactionId, user.id)
            }

            InterceptMessagePayloadType.TRANSACTION_CANCEL -> {
                transactionService.cancel(action.transactionId, user.id)
            }

            InterceptMessagePayloadType.TRANSACTION_RETRY -> {
                notifyTransactionProcessing(user, notificationService, conversationHistoryService)
                transactionService.retry(action.transactionId, user.id)
            }

            else -> {
                conversationHistoryService.historyRepository.saveSystemMessage(user.id, "actionType inválido: ${action.type}")
                return InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
            }
        }

        return when (transactionResult) {
            is TransactionResult.Success -> {
                conversationHistoryService.createSystemMessage(user.id, "Transação confirmada com sucesso")
                InterceptionResult.INTERCEPTED
            }

            is TransactionResult.TransactionError -> {
                val result = if (transactionResult.notifyUser) {
                    when (transactionResult.reason) {
                        is TransactionErrorReason.GenericReason -> handleGenericSweepingError(user, transactionResult.reason.message)
                        is TransactionErrorReason.SweepingLimitExceeded -> handleLimitExceededSweepingError(user, transactionResult.reason.limitType)
                    }
                    InterceptionResult.INTERCEPTED
                } else {
                    InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
                }

                conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Erro processando transação")
                result
            }

            TransactionResult.TransactionNotFound -> {
                conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Transação não encontrada")
                InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
            }

            is TransactionResult.IllegalState -> {
                conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Transação não está ativa: ${transactionResult.status}")
                InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
            }

            is TransactionResult.IllegalUser -> {
                conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Transação pertence a outro usuário: ${transactionResult.userId}")
                InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
            }

            TransactionResult.Canceled -> {
                conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Transação cancelada")
                notifyTransactionCanceled(user, notificationService, conversationHistoryService)
                InterceptionResult.INTERCEPTED
            }
        }
    }

    private fun handleLimitExceededSweepingError(
        user: User,
        limitType: SweepingLimitType,
    ) {
        val (configurationKey, notitifacionType) = when (limitType) {
            SweepingLimitType.DAILY -> KnownTemplateConfigurationKeys.sweepingDailyLimitExceed to KnownNotificationTypes.SWEEPING_DAILY_LIMIT_EXCEED
            else -> KnownTemplateConfigurationKeys.sweepingLimitExceed to KnownNotificationTypes.SWEEPING_LIMIT_EXCEED
        }

        notificationService.notify(
            ChatbotRawTemplatedNotification(
                accountId = user.accountId,
                mobilePhone = user.id.value,
                clientId = tenantService.getConfiguration().clientId,
                configurationKey = configurationKey,
                notificationType = notitifacionType,
            ),
        )
        conversationHistoryService.createAssistantMessage(user.id, "Limite excedido")
    }

    private fun handleGenericSweepingError(
        user: User,
        message: String,
    ) {
        notificationService.notify(
            userId = user.id,
            accountId = user.accountId,
            message = message,
        )
        conversationHistoryService.createAssistantMessage(user.id, message)
    }

    private fun interceptSendPixCodeMessage(
        user: User,
        action: InterceptAction,
    ): InterceptionResult {
        val logName = "ConversationProcessor#interceptSendPixCodeMessage"
        val markers = append("action", action)

        onePixPayInstrumentation.userQuickRepliedPayAll(user)

        val state =
            conversationHistoryService.refreshPendingBills(userId = user.id).getOrElse {
                markers.andAppend("refreshPendingBillsError", it)
                when (it) {
                    is RefreshUserStateError.PendingBillsOutdated -> {
                        onePixPayInstrumentation.billsChangedAfterInteraction(user, InterceptMessagePayloadType.SEND_PIX_CODE)
                        sendBillsOutdatedNotification(user)
                        logger.info(markers, logName)
                        return InterceptionResult.INTERCEPTED
                    }
                }
            }

        val bills = if (action.transactionId != null && action.transactionId.value.isNotEmpty()) {
            val transactionBills = getBillsFromTransaction(action.transactionId, state)
            markers.andAppend("transactionBills", transactionBills?.map { it.billId.value })

            if (transactionBills == null) {
                val notification = "Esta transação não foi encontrada"
                notificationService.notify(
                    userId = state.user.id,
                    accountId = state.user.accountId,
                    notification,
                )
                logger.info(markers, logName)
                return InterceptionResult.INTERCEPTED
            }

            transactionBills
        } else {
            state.walletWithBills.bills
        }
        markers.andAppend("bills", bills.map { it.billId.value })

        conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Usuário clicou no botão 'Pagar tudo com 1 pix'")

        logger.info(markers, logName)
        return interceptMessage(user) {
            Action.MakePayment(
                bills = bills.map { it.externalBillId },
                type = "PIX",
            )
        }
    }

    private fun getPixTransactionDetails(
        user: User,
        action: InterceptAction,
    ): Either<InterceptionResult, PixTransactionDetails> {
        val notification = if (action.transactionId != null && action.transactionId.value.isNotEmpty()) {
            val transaction = transactionService.find(action.transactionId)

            when (transaction?.status) {
                TransactionStatus.ACTIVE -> {
                    if (transaction.details is PixTransactionDetails) {
                        return transaction.details.right()
                    } else {
                        "Esta transação não é do tipo esperado"
                    }
                }

                TransactionStatus.COMPLETED -> "Esta transação já foi completada"
                TransactionStatus.EXPIRED -> "Esta transação já expirou"
                TransactionStatus.CANCELED -> "Esta transação foi cancelada"
                null -> "Esta transação não foi encontrada"
            }
        } else {
            "Esta transação não foi encontrada"
        }

        notificationService.notify(
            userId = user.id,
            accountId = user.accountId,
            notification,
        )
        return InterceptionResult.INTERCEPTED.left()
    }

    private fun Transaction.checkSelectedBills(state: BillComingDueHistoryState, markers: LogstashMarker, type: InterceptMessagePayloadType): Either<CheckSelectedBillsError, List<BillView>> {
        return when (details) {
            is SweepingTransactionDetails -> {
                state.walletWithBills.bills
                    .filter { it.billId in details.bills }.right()
            }

            is ScheduleBillsTransactionDetails -> {
                state.walletWithBills.bills
                    .filter { it.billId in details.bills }.right()
            }

            else -> {
                return CheckSelectedBillsError.InvalidTransactionDetailsType.left()
            }
        }
    }

    private fun TransactionStatus.toGetActiveTransactionError(message: String) = GetActiveTransactionError.InvalidStatus(
        status = this,
        message = message,
    ).left()

    private fun TransactionService.getActiveTransaction(transactionId: TransactionId): Either<GetActiveTransactionError, Transaction> {
        val transaction = transactionService.find(transactionId)

        return when (transaction?.status) {
            null -> GetActiveTransactionError.NotFound(
                transactionId = transactionId,
                message = "Esta transação não foi encontrada",
            ).left()

            TransactionStatus.COMPLETED -> transaction.status.toGetActiveTransactionError("Esta transação já foi completada")
            TransactionStatus.EXPIRED -> transaction.status.toGetActiveTransactionError("Esta transação já expirou")
            TransactionStatus.CANCELED -> transaction.status.toGetActiveTransactionError("Esta transação foi cancelada")
            TransactionStatus.ACTIVE -> transaction.right()
        }
    }

    private fun interceptSendScheduleBillsMessage(
        user: User,
        action: InterceptAction,
    ): InterceptionResult {
        val logName = "ConversationService#interceptSendScheduleBillsMessage"
        val markers = append("action", action)

        val state = conversationHistoryService.refreshPendingBills(userId = user.id).getOrElse {
            when (it) {
                is RefreshUserStateError.PendingBillsOutdated -> {
                    onePixPayInstrumentation.billsChangedAfterInteraction(user, action.type)
                    sendBillsOutdatedNotification(user)
                    return InterceptionResult.INTERCEPTED
                }
            }
        }

        if (action.transactionId != null && action.transactionId.value.isNotEmpty()) {
            val activeTransaction = transactionService.getActiveTransaction(action.transactionId).getOrElse {
                notificationService.notify(
                    userId = state.user.id,
                    accountId = state.user.accountId,
                    message = it.message,
                )

                logger.warn(markers.andAppend("error", it), logName)
                return InterceptionResult.INTERCEPTED
            }
            markers.andAppend("transaction", activeTransaction)

            val selectedBills = activeTransaction.checkSelectedBills(state, markers, action.type).getOrElse {
                when (it) {
                    CheckSelectedBillsError.InvalidTransactionAmount -> {
                        logger.warn(markers, logName)
                        val notification = "O valor da transação não corresponde ao valor das contas"
                        notificationService.notify(
                            userId = state.user.id,
                            accountId = state.user.accountId,
                            notification,
                        )
                        conversationHistoryService.createAssistantMessage(user.id, notification)
                        return InterceptionResult.INTERCEPTED
                    }

                    CheckSelectedBillsError.BillsNotActive -> {
                        logger.warn(markers, logName)
                        val notification = "Uma ou mais contas não estão mais ativas ou já foram pagas"
                        notificationService.notify(
                            userId = state.user.id,
                            accountId = state.user.accountId,
                            notification,
                        )
                        conversationHistoryService.createAssistantMessage(user.id, notification)
                        return InterceptionResult.INTERCEPTED
                    }

                    CheckSelectedBillsError.InvalidTransactionDetailsType -> {
                        logger.warn(markers, logName)
                        val notification = DEFAULT_ERROR_MESSAGE
                        notificationService.notify(
                            userId = state.user.id,
                            accountId = state.user.accountId,
                            notification,
                        )
                        conversationHistoryService.createAssistantMessage(user.id, DEFAULT_ERROR_MESSAGE)
                        return InterceptionResult.INTERCEPTED
                    }
                }
            }
            markers.andAppend("selectedBills", selectedBills)
            logger.info(markers.andAppend("userId", user.id.value), "$logName#${action.type}")

            conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Usuário confirmou o agendamento das contas.")
            val interceptionResult = interceptMessage(user) {
                Action.MakePayment(
                    bills = selectedBills.map { it.externalBillId },
                    type = "SCHEDULE",
                    confirmation = true,
                )
            }
            if (interceptionResult == InterceptionResult.INTERCEPTED && action.type == InterceptMessagePayloadType.SCHEDULE_BILLS) {
                transactionService.confirm(action.transactionId, user.id)
            }
            return interceptionResult
        }

        conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Usuário clicou no botão 'Sim, pagar todas'")
        return interceptMessage(user) {
            Action.MakePayment(
                bills = state.walletWithBills.bills.map { it.externalBillId },
                type = "SCHEDULE",
                confirmation = true,
            )
        }
    }

    fun interceptValidateBoleto(user: User, action: InterceptAction): InterceptionResult {
        val logName = "ConversationService#interceptValidateBoleto"
        val markers = append("userId", user.id.value)
            .andAppend("action", action)

        val transaction = action.transactionId?.let { transactionService.find(it) }

        if (transaction == null || transaction.status != TransactionStatus.ACTIVE) {
            val notification = "Esta transação não foi encontrada"

            conversationHistoryService.createAssistantMessage(user.id, notification)
            notificationService.notify(
                userId = user.id,
                accountId = user.accountId,
                notification,
            )
            logger.warn(markers, logName)
            return InterceptionResult.INTERCEPTED
        }

        val details = transaction.details as AddBoletoTransactionDetails

        return interceptMessage(user) {
            Action.ValidateBoleto(
                barcodes = details.barCodes,
            )
        }
    }

    fun interceptAddBoleto(
        user: User,
        action: InterceptAction,
    ): InterceptionResult {
        val logName = "ConversationService#interceptAddBoleto"
        val markers = append("userId", user.id.value)
            .andAppend("action", action)

        val transaction = action.transactionId?.let { transactionService.find(it) }
        if (transaction == null || transaction.status != TransactionStatus.ACTIVE) {
            val notification = "Este botão não é mais válido"

            conversationHistoryService.createAssistantMessage(user.id, notification)
            notificationService.notify(
                userId = user.id,
                accountId = user.accountId,
                notification,
            )
            logger.warn(markers, logName)
            return InterceptionResult.INTERCEPTED
        }

        val transactionDetails = transaction.details as AddBoletoTransactionDetails

        if (transactionDetails.barCodes.isEmpty()) {
            val notification = "Não consegui encontrar o código de barras no boleto"
            notificationService.notify(
                userId = user.id,
                accountId = user.accountId,
                notification,
            )
            logger.warn(markers, logName)
            return InterceptionResult.INTERCEPTED
        }

        if (transactionDetails.barCodes.size == 1) {
            val scheduleTransaction = addBillAndCreateScheduleTransaction(transactionDetails, user, markers, logName).getOrElse { return it }

            markers.andAppend("scheduleTransaction", scheduleTransaction)

            val quickReplies = listOf(
                QuickReplyButton(
                    text = "Pagar Agora",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.PAY_BOLETO.name,
                            transactionId = scheduleTransaction.id.value,
                        ),
                    ),
                ),
                QuickReplyButton(
                    text = "Pagar no vencimento",
                    payload = getObjectMapper().writeValueAsString(
                        BlipPayload(
                            payload = getLocalDate().format(dateFormat),
                            action = InterceptMessagePayloadType.SCHEDULE_BOLETO.name,
                            transactionId = scheduleTransaction.id.value,
                        ),
                    ),
                ),

                )
            val message = "O boleto foi adicionado no app! Você pode pagar agora ou agendar o pagamento para o vencimento."
            customNotificationService.send(
                mobilePhone = user.id.value,
                accountId = user.accountId,
                message = message,
                quickReplies = quickReplies,
            )
        } else {
            if (transactionDetails.barCodes.size > 60) {
                val notification = "Não consigo adicionar mais de 60 boletos de uma vez. Por favor, tente novamente com menos boletos."
                notificationService.notify(
                    userId = user.id,
                    accountId = user.accountId,
                    notification,
                )
                logger.warn(markers, logName)
                return InterceptionResult.INTERCEPTED
            }

            val message = "Ok, vou adicionar os boletos para você, em instantes eles estarão na sua carteira."
            conversationHistoryService.createAssistantMessage(user.id, message)
            notificationService.notify(
                userId = user.id,
                accountId = user.accountId,
                message = message,
            )

            transactionDetails.barCodes.forEach { details ->
                val validBarcode = try {
                    details.codigo.toBarCode()
                } catch (e: Exception) {
                    return@forEach
                }

                val addBillResult = paymentAdapter.addBill(
                    userId = user.id,
                    digitable = validBarcode.digitable,
                    dueDate = details.vencimento,
                    dryRun = false,
                ).getOrElse {
                    markers.andAppend("paymentAdapterError", it)

                    logger.warn(markers, logName)
                    return@forEach
                }
                markers.andAppend("addBillResult", addBillResult)
            }
        }

        logger.info(markers, logName)

        return InterceptionResult.INTERCEPTED
    }

    fun interceptPayBoleto(
        user: User,
        interceptAction: InterceptAction,
        scheduleToDue: Boolean = false,
    ): InterceptionResult {
        val logName = "ConversationService#interceptPayBoleto"
        val markers = append("userId", user.id.value)
            .andAppend("action", interceptAction)

        val transaction = interceptAction.transactionId?.let { transactionService.find(it) }
        if (transaction == null || transaction.status != TransactionStatus.ACTIVE) {
            val notification = "Este botão não está mais válido"
            customNotificationService.send(mobilePhone = user.id.value, accountId = user.accountId, message = notification)
            logger.warn(markers, logName)
            return InterceptionResult.INTERCEPTED
        }

        val scheduleTransaction = when (transaction.details) {
            is ScheduleBillsTransactionDetails -> transaction
            is AddBoletoTransactionDetails -> addBillAndCreateScheduleTransaction(transactionDetails = transaction.details, user = user, markers = markers, logName = logName).getOrElse { return it }
            else -> {
                val notification = "Esta transação não é válida"
                customNotificationService.send(mobilePhone = user.id.value, accountId = user.accountId, message = notification)
                logger.warn(markers, logName)
                return InterceptionResult.INTERCEPTED
            }
        }

        markers.andAppend("scheduleTransactionId", scheduleTransaction.id.value)

        logger.info(markers, logName)
        return interceptMessage(user) {
            Action.ScheduleBoleto(
                transactionId = scheduleTransaction.id,
                scheduleToDueDate = scheduleToDue,
            )
        }
    }

    fun interceptRetrySweepingTransfer(
        user: User,
        action: InterceptAction,
    ): InterceptionResult {
        val logName = "ConversationService#interceptRetrySweepingTransfer"
        val markers = append("userId", user.id.value)
            .andAppend("action", action)

        if (action.amount == null) {
            conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)
            conversationHistoryService.createSystemMessage(user.id, "Valor não informado.")
            logger.warn(markers, logName)
            return InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
        }

        val currentState = conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId = user.id)

        val walletId = currentState.walletWithBills.walletId
        markers.andAppend("walletId", walletId?.value)

        if (walletId == null) {
            conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)
            conversationHistoryService.createSystemMessage(user.id, "Carteira do usuário não encontrada.")
            logger.warn(markers, logName)
            return InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
        }

        notifyTransactionProcessing(user, notificationService, conversationHistoryService)

        paymentAdapter.retrySweepingTransfer(
            userId = user.id,
            walletId = walletId,
            amount = action.amount,
            participantId = action.transactionId?.let { SweepingParticipantId(it.value) },
        ).onLeft {
            markers.andAppend("error", it)
            when (it) {
                is SweepingTransferErrorReason.GenericReason -> handleGenericSweepingError(user, it.message)
                is SweepingTransferErrorReason.LimitExceeded -> handleLimitExceededSweepingError(user, it.limitType)
            }
        }.onRight {
            markers.andAppend("endToEnd", it)
        }

        logger.info(markers, logName)
        return InterceptionResult.INTERCEPTED
    }

    private fun interceptSendSubscriptionPixCodeMessage(
        user: User,
    ): InterceptionResult {
        conversationHistoryService.historyRepository.saveSystemMessage(user.id, "Usuário clicou no botão 'Pagar assinatura'")

        val currentState = conversationHistoryService.findLatestConversationHistoryState<BillComingDueHistoryState>(userId = user.id)

        val walletId = currentState.walletWithBills.walletId
        if (walletId == null) {
            conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)
            conversationHistoryService.createSystemMessage(user.id, "Carteira do usuário não encontrada.")
            return InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
        }

        val subscriptionAmount =
            paymentAdapter.getSubscriptionFee(userId = user.id).getOrElse {
                logger.warn(append("reason", it), "interceptSendSubscriptionPixCodeMessage#getSubscriptionFee")
                SUBSCRIPTION_FALLBACK_VALUE
            }
        val currentBalance = currentState.balance?.current ?: 0

        if (currentBalance >= subscriptionAmount) {
            notificationService.notify(
                user.id,
                user.accountId,
                "Você já possui saldo na sua carteira para o pagamento da assinatura.",
            )

            conversationHistoryService.createAssistantMessage(user.id, "Usuário já tem saldo na carteira para pagar assinatura.")

            return InterceptionResult.INTERCEPTED
        }

        val notificationMessage =
            if (currentBalance > 0) {
                """
                |Sua assinatura será paga automaticamente ao adicionar saldo à sua carteira.
                |    
                |Saldo na carteira: ${NotificationFormatter.buildFormattedAmount(currentBalance)}
                |Valor da assinatura: ${NotificationFormatter.buildFormattedAmount(subscriptionAmount)}
                |
                |Use o código Pix copia e cola abaixo:
                """.trimMargin()
            } else {
                """
                |Sua assinatura será paga automaticamente ao adicionar saldo à sua carteira.
                |
                |Valor da assinatura: ${NotificationFormatter.buildFormattedAmount(subscriptionAmount)}
                | 
                |Use o código Pix copia e cola abaixo:
                """.trimMargin()
            }
        notificationService.notify(user.id, user.accountId, notificationMessage)

        val pixCode =
            runBlocking {
                paymentAdapter.generatePixQRCode(
                    userId = user.id,
                    amount = subscriptionAmount - currentBalance,
                    walletId = walletId,
                    message = "Pagamento assinatura Friday",
                )
            }.getOrElse {
                conversationHistoryService.createSystemMessage(user.id, "Não conseguiu gerar o código Pix.")
                return InterceptionResult.INTERCEPTED_BUT_NEED_COMPLETION
            }
        notificationService.notify(user.id, user.accountId, pixCode.value, delay = 1)

        conversationHistoryService.createAssistantMessage(user.id, "código pix para pagamento da assinatura enviado ao usuário.")

        conversationHistoryService.synchronizeBeforeCompletion(userId = user.id)

        return InterceptionResult.INTERCEPTED
    }

    private fun getBillComingDueState(user: User): BillComingDueHistoryState =
        when (val state = conversationHistoryService.historyStateRepository.findLatest(user.id)) {
            is BillComingDueHistoryState -> state
        }

    private fun getBillsFromTransaction(transactionId: TransactionId, state: BillComingDueHistoryState): List<BillView>? {
        val transaction = transactionService.find(transactionId) ?: return null

        return when (val details = transaction.details) {
            is ScheduleBillsTransactionDetails -> state.walletWithBills.bills.filter { bill -> details.bills.find { it == bill.billId } != null }
            is SweepingTransactionDetails -> state.walletWithBills.bills.filter { bill -> details.bills.find { it == bill.billId } != null }
            is SendPixCodeTransactionDetails -> state.walletWithBills.bills.filter { bill -> details.bills.find { it == bill.billId } != null }
            is PixTransactionDetails, is AddBoletoTransactionDetails -> throw IllegalStateException("transaction does not contain bills")
            is MarkAsPaidOrIgnoreBillsTransactionDetails -> throw IllegalStateException("Transaction does not have a single list of bills")
        }
    }

    private fun interceptPaySome(
        user: User,
    ): InterceptionResult {
        val message = notificationContextTemplatesService.getPaySomeReplyMessage()

        return interceptMessage(user) {
            Action.SendMessage(message)
        }
    }

    private fun addBillAndCreateScheduleTransaction(
        transactionDetails: AddBoletoTransactionDetails,
        user: User,
        markers: LogstashMarker,
        logName: String,
    ): Either<InterceptionResult, Transaction> {
        val details = transactionDetails.barCodes.first()
        val validBarcode = details.codigo.toBarCode()

        val addBillResult = paymentAdapter.addBill(
            userId = user.id,
            digitable = validBarcode.digitable,
            dueDate = details.vencimento,
            dryRun = false,
        ).getOrElse {
            markers.andAppend("paymentAdapterError", it)

            val errorMessage = """
                        |Não consegui adicionar o boleto ${validBarcode.digitable}.
                        |
                        |${it.toActionMessage().message}
            """.trimMargin()

            customNotificationService.send(
                mobilePhone = user.id.value,
                accountId = user.accountId,
                message = errorMessage,
            )
            logger.warn(markers, logName)
            return InterceptionResult.INTERCEPTED.left()
        }

        markers.andAppend("addBillResult", addBillResult)

        val scheduleTransaction = transactionService.create(
            userId = user.id,
            walletId = WalletId(user.accountId.value),
            details = ScheduleBillsTransactionDetails(
                bills = listOf(addBillResult.billId),
            ),
        )
        return scheduleTransaction.right()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ConversationProcessor::class.java)
    }
}

enum class InterceptionResult {
    INTERCEPTED,
    INTERCEPTED_BUT_NEED_COMPLETION,
}

enum class InterceptMessagePayloadType(
    val expectedTexts: List<String>,
    val expectedPrefixes: List<String> = emptyList(),
    val allowedOutdated: Boolean = false,
    val allowEmptyText: Boolean = false,
) {
    MARK_AS_PAID(listOf("Já paguei", "Sim"), allowedOutdated = true),
    SEND_PIX_CODE(listOf("Pagar tudo com 1 Pix", "Pagar com 1 Pix", "Prefiro pagar com 1 Pix", "Sim, pagar todas", "Sim, pagar este")),
    SEND_SUBSCRIPTION_PIX_CODE(listOf("Pagar Assinatura", "Rever assinatura")),
    TRANSACTION_CONFIRM(listOf("Sim"), expectedPrefixes = listOf("Sim, usar ", "Usar ")),
    TRANSACTION_CANCEL(listOf("Não", "Não quero pagar", "Não autorizar", "Dados incorretos")),
    TRANSACTION_RETRY(listOf("Tentar novamente")),
    RETRY_SWEEPING(listOf("Tentar novamente")),
    MARK_REMINDER_AS_DONE(listOf("Marcar como resolvido")),
    LIST_BILLS(listOf("Ver contas", "Ver todas", "Ver todos"), allowedOutdated = true),
    ADD_NEW_CONNECTION(listOf("Conectar mais contas")),
    ONBOARDING_SINGLE_PIX(listOf("Testar", "Vamos lá", "Mais tarde", "Sim, pode pagar", "Não", "Sim")),
    SCHEDULE_BILLS(listOf("Sim, pagar todas", "Sim", "Sim, pagar este", "Sim, pagar todos")),
    SCHEDULE_CONFIRM(listOf("Sim")), // FIXME poderia ser removido e só usar o SCHEDULE_BILLS, mas precisa verificar TODAS as mensagens enviadas por template ou que nascem no bill payment
    PROMOTE_SWEEPING_ACCOUNT(listOf("Saber mais", "Quero conhecer", "Mais informações", "Não tenho interesse", "Não quero")),
    PAY_SOME(listOf("Pagar algumas", "Pagar alguns")),
    ADD_BOLETO(listOf("Sim", "Incluir no App")),
    PAY_BOLETO(listOf("Pagar agora")),
    SCHEDULE_BOLETO(listOf("Pagar no vencimento")),
    VALIDATE_BOLETO(listOf("Validar boleto"), allowEmptyText = true),
    REMOVE_MANUAL_ENTRY(listOf("Desfazer")),
    SELECT_SWEEPING_ACCOUNT(listOf("Sim, com outra conta")),
}

data class InterceptAction(
    val type: InterceptMessagePayloadType,
    val transactionId: TransactionId? = null,
    val payload: String? = null,
    val amount: Long? = null,
)

typealias ExternalBillId = Int

sealed class CheckSelectedBillsError : PrintableSealedClass() {
    object InvalidTransactionAmount : CheckSelectedBillsError()
    object InvalidTransactionDetailsType : CheckSelectedBillsError()
    object BillsNotActive : CheckSelectedBillsError()
}

sealed interface GetActiveTransactionError {
    val message: String

    data class NotFound(val transactionId: TransactionId, override val message: String) : GetActiveTransactionError
    data class InvalidStatus(val status: TransactionStatus, override val message: String) : GetActiveTransactionError
}