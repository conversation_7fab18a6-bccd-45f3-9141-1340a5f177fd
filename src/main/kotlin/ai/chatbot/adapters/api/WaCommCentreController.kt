package ai.chatbot.adapters.api

import ai.chatbot.adapters.waCommCentre.InboundMessage
import ai.chatbot.adapters.waCommCentre.IncomingMessageProcessor
import ai.chatbot.app.config.MdcContext
import ai.chatbot.app.config.TenantService
import ai.chatbot.app.user.UserId
import ai.chatbot.app.utils.andAppend
import io.micronaut.core.async.publisher.Publishers
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Filter
import io.micronaut.http.annotation.Post
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import jakarta.inject.Named
import java.util.concurrent.ExecutorService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import net.logstash.logback.marker.Markers.append
import org.reactivestreams.Publisher
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Filter(value = ["/message/**"])
class WaCommCentreControllerFilter(
    private val tenantService: TenantService,
) : HttpServerFilter {
    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun doFilter(
        request: HttpRequest<*>,
        chain: ServerFilterChain,
    ): Publisher<MutableHttpResponse<*>> {
        val auth = tenantService.getConfiguration().waCommCentre.auth
        if (BasicAuthValidator.validate(request, auth.clientId, auth.secret)) {
            return chain.proceed(request)
        }

        logger.warn(append("authorization", request.headers["authorization"]), "WaCommCentreControllerFilter")
        return Publishers.just(HttpResponse.status<Any>(HttpStatus.UNAUTHORIZED))
    }
}

@Controller("/message")
class WaCommCentreController(
    private val incomingMessageProcessor: IncomingMessageProcessor,
    @Named("message-handler") executorService: ExecutorService,
    private val tenantService: TenantService,
) {
    private val logName = "WaCommCentreController"
    private val dispatcher = executorService.asCoroutineDispatcher()

    @Post
    suspend fun message(
        @Body body: InboundMessage,
    ): MutableHttpResponse<String>? {
        val markers = append("body", body).andAppend("userId", body.from)

        if (!tenantService.getConfiguration().waCommCentre.enabled) {
            return HttpResponse.notFound()
        }

        if (!tenantService.getConfiguration().flags.waCommCentre.check(UserId(body.from))) {
            logger.info(markers, "$logName/disabled")
            return HttpResponse.ok("ok")
        }

        logger.info(markers, "$logName#start")

        CoroutineScope(dispatcher + MdcContext()).launch {
            incomingMessageProcessor.process(body)
        }

        return HttpResponse.ok("ok")
    }

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(WaCommCentreController::class.java)
    }
}