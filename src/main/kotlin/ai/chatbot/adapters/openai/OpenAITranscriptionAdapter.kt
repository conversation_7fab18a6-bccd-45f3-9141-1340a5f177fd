package ai.chatbot.adapters.openai

import ai.chatbot.app.file.ObjectRepository
import ai.chatbot.app.file.StoredObject
import ai.chatbot.app.transcription.TranscriptionAdapter
import com.theokanning.openai.audio.CreateTranscriptionRequest
import com.theokanning.openai.service.OpenAiService
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.io.File
import java.io.InputStream
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@Requires(property = "openai.transcription.enabled", value = "true")
class OpenAITranscriptionAdapter(
    @Property(name = "openai.transcription.model") private val model: String,
    @Property(name = "openai.transcription.language") private val language: String,
    private val aiService: OpenAiService,
    private val objectRepository: ObjectRepository,
) : TranscriptionAdapter {

    override val name: String = "openai"

    override suspend fun transcribe(storedObject: StoredObject): String? {
        val input: InputStream = objectRepository.loadObject(storedObject.bucket, storedObject.key)
        val request = CreateTranscriptionRequest.builder()
            .model(model)
            .language(language)
            .build()

        val fileExtension = storedObject.key.substringAfterLast('.', "")

        val tempFile = File.createTempFile("openai-audio-transcription", ".$fileExtension")

        tempFile.outputStream().use { output ->
            input.copyTo(output)
        }

        return aiService.createTranscription(request, tempFile).also { result ->
            logger.info(append("result", result), "OpenAITranscriptionAdapter#transcribe")
        }.text
    }

    companion object {
        private val logger = LoggerFactory.getLogger(OpenAITranscriptionAdapter::class.java)
    }
}